import fetch from "node-fetch";

const testRecommendations = async () => {
  try {
    // Test with sample data from our debug script
    const testParams = new URLSearchParams({
      foodChainId: "67d3068336223ee92a96456d",
      message: "What should I order?",
      lastConversation: JSON.stringify([]),
      userId: "67d3068336223ee92a96456d", // Using same ID for testing
      outletId: "67d46268bde37643fa92b2cc",
      language: "en",
    });

    const url = `http://localhost:3001/api/v1/user/conversation?${testParams.toString()}`;

    console.log("Testing URL:", url);

    const response = await fetch(url);

    if (!response.ok) {
      console.error("Response not OK:", response.status, response.statusText);
      return;
    }

    console.log("Response headers:", response.headers.raw());

    // Since it's a streaming response, we need to handle it differently
    let buffer = "";

    response.body.on("data", (chunk) => {
      buffer += chunk.toString();

      // Process complete lines
      const lines = buffer.split("\n");
      buffer = lines.pop() || ""; // Keep incomplete line in buffer

      for (const line of lines) {
        if (line.trim()) {
          console.log("Received line:", line);

          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.substring(6));
              console.log("Parsed data:", JSON.stringify(data, null, 2));
            } catch (e) {
              console.log("Could not parse JSON:", line);
            }
          }
        }
      }
    });

    response.body.on("end", () => {
      console.log("Stream ended");
    });

    response.body.on("error", (error) => {
      console.error("Stream error:", error);
    });

    // Wait for the stream to complete
    await new Promise((resolve, reject) => {
      response.body.on("end", resolve);
      response.body.on("error", reject);
    });
  } catch (error) {
    console.error("Error testing recommendations:", error);
  }
};

testRecommendations();

import mongoose from "mongoose";
import Order from "../models/Order.js";
import Payment from "../models/Payment.js";
import FoodChain from "../models/FoodChain.js";
import FundTransfer from "../models/FundTransfer.js";
import {
  createPaymentLink,
  getPaymentLinkDetails,
  verifyPaymentSignature,
} from "../utils/razorpay.js";
import { createPayout } from "../utils/razorpayTransfer.js";
import { emitPaymentUpdate } from "../sockets/orderSocket.js";
import {
  createOrderPaymentNotification,
  createFundTransferNotification,
} from "../services/payment-notification-service.js";
import crypto from "crypto";
import connectDB from "../config/database.js";

/**
 * Map Razorpay payout status to our FundTransfer status enum
 * @param {String} razorpayStatus - The Razorpay payout status
 * @returns {String} - The corresponding FundTransfer status
 */
export const mapRazorpayStatusToFundTransferStatus = (razorpayStatus) => {
  switch (razorpayStatus) {
    case "processed":
      return "completed";
    case "processing":
      return "processing";
    case "reversed":
      return "reversed";
    case "failed":
      return "failed";
    case "completed":
      return "completed";
    // Transfer API specific statuses
    case "created":
      return "initiated";
    case "pending":
      return "processing";
    case "captured":
    case "onhold":
      return "processing";
    case "refunded":
      return "reversed";
    default:
      return "initiated";
  }
};

/**
 * Create a payment request for an order
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createPaymentRequest = async (req, res) => {
  try {
    const { orderId } = req.params;
    const foodChainId = req.user.foodChain;

    console.log(
      `Creating payment request for order ID: ${orderId}, foodChainId: ${foodChainId}`
    );

    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      console.error(`Invalid order ID format: ${orderId}`);
      return res.status(400).json({
        success: false,
        message: "Invalid order ID format",
      });
    }

    // First try to find the order with the food chain ID
    let order = await Order.findOne({ _id: orderId, foodChainId })
      .populate("userId", "name phone email")
      .populate("outletId", "name address");

    // If not found, try to find the order by ID only (for backward compatibility)
    if (!order) {
      console.log(
        `Order not found with foodChainId ${foodChainId}, trying with ID only`
      );
      order = await Order.findById(orderId)
        .populate("userId", "name phone email")
        .populate("outletId", "name address");

      // If found, update the order with the food chain ID
      if (order) {
        console.log(
          `Order found by ID only, updating with foodChainId ${foodChainId}`
        );
        order.foodChainId = foodChainId;
        await order.save();
      }
    }

    console.log(`Order found: ${order ? "Yes" : "No"}`);

    if (!order) {
      console.error(
        `Order not found for ID: ${orderId} and foodChainId: ${foodChainId}`
      );
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if payment already exists for this order
    const existingPayment = await Payment.findOne({ orderId });
    if (existingPayment) {
      return res.status(200).json({
        success: true,
        message: "Payment request already exists",
        data: existingPayment,
      });
    }

    // Create Razorpay payment link
    const paymentLinkData = await createPaymentLink(order, orderId);
    console.log(paymentLinkData, "paymentLinkData");
    // Create payment record in database
    const paymentAmount =
      order.finalAmount !== undefined ? order.finalAmount : order.totalAmount;
    const payment = new Payment({
      orderId,
      razorpayOrderId: paymentLinkData.notes.orderId,
      razorpayPaymentLinkId: paymentLinkData.id,
      paymentLink: paymentLinkData.short_url,
      amount: paymentAmount,
      status: "created",
    });

    await payment.save();

    // Update order payment status
    order.paymentStatus = "requested";
    order.paymentMethod = "online";
    await order.save();

    // Emit socket event for real-time update
    await emitPaymentUpdate(orderId);

    res.status(201).json({
      success: true,
      message: "Payment request created successfully",
      data: payment,
    });
  } catch (error) {
    console.error("Error creating payment request:", error);
    res.status(500).json({
      success: false,
      message: "Error creating payment request",
      error: error.message,
    });
  }
};

/**
 * Get payment details for an order
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getPaymentDetails = async (req, res) => {
  try {
    const { orderId } = req.params;

    // Find the payment
    const payment = await Payment.findOne({ orderId });
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: "Payment not found",
      });
    }

    // Get latest payment status from Razorpay
    const paymentLinkDetails = await getPaymentLinkDetails(
      payment.razorpayPaymentLinkId
    );

    // Update payment status if changed
    if (paymentLinkDetails.status !== payment.status) {
      payment.status = paymentLinkDetails.status;
      await payment.save();

      // Update order payment status
      const order = await Order.findById(orderId);
      if (order) {
        if (paymentLinkDetails.status === "paid") {
          order.paymentStatus = "paid";
        } else if (paymentLinkDetails.status === "failed") {
          order.paymentStatus = "failed";
        }
        await order.save();

        // Emit socket event for real-time update
        await emitPaymentUpdate(orderId);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        payment,
        razorpayDetails: paymentLinkDetails,
      },
    });
  } catch (error) {
    console.error("Error fetching payment details:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching payment details",
      error: error.message,
    });
  }
};

/**
 * Handle Razorpay webhook for payment updates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const handlePaymentWebhook = async (req, res) => {
  try {
    // Ensure database connection is established
    await connectDB();
    console.log("Webhook received", req.body.event);

    // Verify webhook signature if available
    // Uncomment this code when you have set up the webhook secret in your .env file
    /*
    const webhookSignature = req.headers['x-razorpay-signature'];
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

    if (webhookSignature && webhookSecret) {
      const isValidSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(JSON.stringify(req.body))
        .digest('hex');

      if (webhookSignature !== isValidSignature) {
        return res.status(400).json({ error: 'Invalid webhook signature' });
      }
    }
    */

    const { event, payload } = req.body;
    console.log(`Processing ${event} webhook`);

    // Handle payment_link.paid event
    if (
      event === "payment_link.paid" &&
      payload &&
      payload.payment_link &&
      payload.payment_link.entity
    ) {
      const paymentLinkId = payload.payment_link.entity.id;
      const paymentStatus = "paid"; // For payment_link.paid event, status is always paid

      // Find the payment
      const payment = await Payment.findOne({
        razorpayPaymentLinkId: paymentLinkId,
      });
      if (!payment) {
        return res.status(404).json({ message: "Payment not found" });
      }

      // Update payment status
      payment.status = paymentStatus;
      if (payload.payment && payload.payment.entity) {
        payment.razorpayPaymentId = payload.payment.entity.id;
      }
      await payment.save();

      // Update order payment status
      const order = await Order.findById(payment.orderId).populate(
        "foodChainId"
      );
      if (order) {
        if (paymentStatus === "paid") {
          order.paymentStatus = "paid";

          // Create payment success notification
          await createOrderPaymentNotification(order, payment, "paid");

          // Automatically initiate fund transfer if payment is successful
          try {
            // Import the autoTransferFundsToFoodChain function
            const { autoTransferFundsToFoodChain } = await import(
              "../controllers/razorpay-route-controller.js"
            );

            // Get the Razorpay payment ID from the webhook payload
            let razorpayPaymentId = null;
            if (payload.payment && payload.payment.entity) {
              razorpayPaymentId = payload.payment.entity.id;
            }

            if (razorpayPaymentId) {
              // Use the Route method to transfer funds
              const transferResult = await autoTransferFundsToFoodChain(
                razorpayPaymentId,
                order
              );

              if (transferResult) {
                console.log(
                  `Automatic fund transfer initiated for order ${order._id} using Razorpay Route`
                );

                // Create fund transfer notification
                await createFundTransferNotification(
                  transferResult.fundTransfer,
                  order
                );
              } else {
                console.log(
                  `Automatic fund transfer failed for order ${order._id}. Falling back to old method.`
                );

                // Create transfer failed notification
                await createOrderPaymentNotification(
                  order,
                  payment,
                  "transfer_failed",
                  {
                    error:
                      "Automatic fund transfer failed. Falling back to old method.",
                  }
                );

                // Fallback to old method if Route transfer fails
                // Check if the food chain has a fund account set up
                const foodChain = await FoodChain.findById(order.foodChainId);

                if (foodChain && foodChain.razorpayFundAccountId) {
                  // Create a reference for the transfer
                  const reference = `auto_transfer_${Date.now()}_${
                    order.orderNumber
                  }`;

                  // Calculate transfer amount (minus platform fee if applicable)
                  const platformFeePercentage = 0.05; // 5%
                  const transferAmount =
                    order.totalAmount * (1 - platformFeePercentage);

                  let payout;
                  let payoutError = null;

                  try {
                    // Create payout using the Razorpay API
                    payout = await createPayout({
                      fundAccountId: foodChain.razorpayFundAccountId,
                      amount: transferAmount,
                      reference,
                      description: `Automatic transfer for order #${order.orderNumber}`,
                      transferMethod: "IMPS",
                    });

                    console.log("Created Razorpay payout:", payout);

                    // Create fund transfer notification for successful payout
                    const fundTransfer = new FundTransfer({
                      foodChainId: foodChain._id,
                      amount: transferAmount,
                      razorpayPayoutId: payout.id,
                      razorpayFundAccountId: foodChain.razorpayFundAccountId,
                      status: mapRazorpayStatusToFundTransferStatus(
                        payout.status
                      ),
                      reference: payout.reference,
                      description: `Automatic transfer for order #${order.orderNumber}`,
                      transferMethod: payout.mode,
                      transferFee: payout.fees || 0,
                    });

                    await fundTransfer.save();
                    await createFundTransferNotification(fundTransfer, order);
                  } catch (error) {
                    console.error("Error creating Razorpay payout:", error);
                    payoutError = error;

                    // Create a mock payout object for development/fallback
                    payout = {
                      id: `payout_${Date.now()}`,
                      status: "completed", // Set to completed instead of processing
                      reference: reference,
                      mode: "IMPS",
                      fees: 0,
                    };
                    console.log(
                      "Using fallback payout object with completed status:",
                      payout
                    );

                    // Create notification for payout error
                    await createOrderPaymentNotification(
                      order,
                      payment,
                      "transfer_failed",
                      {
                        error:
                          error.message || "Error creating Razorpay payout",
                      }
                    );
                  }

                  // Create fund transfer record
                  const fundTransfer = new FundTransfer({
                    foodChainId: foodChain._id,
                    amount: transferAmount,
                    razorpayPayoutId: payout.id,
                    razorpayFundAccountId: foodChain.razorpayFundAccountId,
                    // Map Razorpay status to our status enum
                    status: payoutError
                      ? "initiated"
                      : mapRazorpayStatusToFundTransferStatus(payout.status),
                    reference: payout.reference,
                    description: payoutError
                      ? `Automatic transfer for order #${order.orderNumber} (API Error: ${payoutError.message})`
                      : `Automatic transfer for order #${order.orderNumber}`,
                    transferMethod: payout.mode,
                    transferFee: payout.fees || 0,
                  });

                  await fundTransfer.save();
                  console.log(
                    `Automatic fund transfer ${
                      payoutError ? "recorded with error" : "initiated"
                    } for order ${order._id}`
                  );
                } else {
                  console.log(
                    `Food chain ${order.foodChainId} does not have any payment transfer method set up`
                  );

                  // Create notification for missing payment transfer method
                  await createOrderPaymentNotification(
                    order,
                    payment,
                    "transfer_failed",
                    {
                      error:
                        "Food chain does not have any payment transfer method set up",
                    }
                  );
                }
              }
            } else {
              console.log(
                "No Razorpay payment ID found in webhook payload. Cannot initiate transfer."
              );

              // Create notification for missing payment ID
              await createOrderPaymentNotification(
                order,
                payment,
                "transfer_failed",
                {
                  error:
                    "No Razorpay payment ID found in webhook payload. Cannot initiate transfer.",
                }
              );
            }
          } catch (transferError) {
            console.error(
              "Error initiating automatic fund transfer:",
              transferError
            );
            // Don't fail the webhook if transfer fails, just log the error

            // Create notification for transfer error
            await createOrderPaymentNotification(
              order,
              payment,
              "transfer_failed",
              {
                error:
                  transferError.message ||
                  "Unknown error initiating automatic fund transfer",
              }
            );
          }
        } else if (paymentStatus === "failed") {
          order.paymentStatus = "failed";

          // Create payment failure notification
          await createOrderPaymentNotification(order, payment, "failed");
        }
        await order.save();

        // Emit socket event for real-time update
        await emitPaymentUpdate(payment.orderId);
      }
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error("Error handling payment webhook:", error);
    res.status(500).json({
      success: false,
      message: "Error handling payment webhook",
      error: error.message,
    });
  }
};

/**
 * Verify payment after successful completion
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
/**
 * Handle Razorpay payout webhook for fund transfer status updates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const handlePayoutWebhook = async (req, res) => {
  try {
    // Ensure database connection is established
    await connectDB();
    console.log("Payout webhook received", req.body.event);

    // Verify webhook signature if available
    // Uncomment this code when you have set up the webhook secret in your .env file
    /*
    const webhookSignature = req.headers['x-razorpay-signature'];
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

    if (webhookSignature && webhookSecret) {
      const isValidSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(JSON.stringify(req.body))
        .digest('hex');

      if (webhookSignature !== isValidSignature) {
        return res.status(400).json({ error: 'Invalid webhook signature' });
      }
    }
    */

    const { event, payload } = req.body;
    console.log(`Processing ${event} webhook`);

    // Handle transfer events
    if (
      (event === "transfer.processed" || event === "transfer.failed") &&
      payload &&
      payload.payout &&
      payload.payout.entity
    ) {
      const payoutId = payload.payout.entity.id;
      const payoutStatus = payload.payout.entity.status;

      // Find the fund transfer
      const fundTransfer = await FundTransfer.findOne({
        razorpayPayoutId: payoutId,
      });

      if (!fundTransfer) {
        return res.status(404).json({ message: "Fund transfer not found" });
      }

      // Update fund transfer status
      const newStatus = mapRazorpayStatusToFundTransferStatus(payoutStatus);

      if (fundTransfer.status !== newStatus) {
        fundTransfer.status = newStatus;

        // If failed, store failure reason
        if (payoutStatus === "failed" && payload.payout.entity.failure_reason) {
          fundTransfer.failureReason = payload.payout.entity.failure_reason;
        }

        await fundTransfer.save();
        console.log(
          `Updated fund transfer ${fundTransfer._id} status to ${newStatus}`
        );

        // Find the order associated with this fund transfer
        const order = await Order.findOne({
          _id: {
            $in: await Payment.distinct("orderId", {
              razorpayPaymentId: fundTransfer.razorpayPayoutId,
            }),
          },
        });

        // Create fund transfer notification
        await createFundTransferNotification(fundTransfer, order);
      }
    }

    // Handle fund account validation events
    if (
      event === "fund_account.validation.completed" &&
      payload &&
      payload.fund_account &&
      payload.fund_account.entity
    ) {
      const fundAccountId = payload.fund_account.entity.id;
      console.log(`Fund account ${fundAccountId} validation completed`);

      // Find food chain with this fund account ID
      const foodChain = await FoodChain.findOne({
        razorpayFundAccountId: fundAccountId,
      });
      if (foodChain) {
        // You could update a validation status field if you have one
        console.log(`Fund account validated for food chain: ${foodChain.name}`);
      }
    }

    // Handle fund account validation failure
    if (
      event === "fund_account.validation.failed" &&
      payload &&
      payload.fund_account &&
      payload.fund_account.entity
    ) {
      const fundAccountId = payload.fund_account.entity.id;
      console.log(`Fund account ${fundAccountId} validation failed`);

      // Find food chain with this fund account ID
      const foodChain = await FoodChain.findOne({
        razorpayFundAccountId: fundAccountId,
      });
      if (foodChain) {
        // You could update a validation status field if you have one
        console.log(
          `Fund account validation failed for food chain: ${foodChain.name}`
        );
      }
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error("Error handling payout webhook:", error);
    res.status(500).json({
      success: false,
      message: "Error handling payout webhook",
      error: error.message,
    });
  }
};

/**
 * Verify payment after successful completion
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const verifyPayment = async (req, res) => {
  try {
    const {
      razorpayPaymentId,
      razorpayOrderId,
      razorpaySignature,
      paymentLinkId,
      paymentLinkStatus,
    } = req.body;

    // Find the payment by payment link ID
    const payment = await Payment.findOne({
      razorpayPaymentLinkId: paymentLinkId,
    });
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: "Payment not found",
      });
    }

    // Update payment with payment ID if available
    if (razorpayPaymentId && !payment.razorpayPaymentId) {
      payment.razorpayPaymentId = razorpayPaymentId;
    }

    // Update payment status if it's completed
    if (paymentLinkStatus === "paid" && payment.status !== "paid") {
      payment.status = "paid";
      await payment.save();

      // Update order payment status
      const order = await Order.findById(payment.orderId)
        .populate("userId", "name phone email")
        .populate("outletId", "name address")
        .populate("items.dishId", "name price");

      if (order) {
        order.paymentStatus = "paid";
        await order.save();

        // Emit socket event for real-time update
        await emitPaymentUpdate(payment.orderId);

        return res.status(200).json({
          success: true,
          message: "Payment verified successfully",
          data: {
            order,
            payment,
          },
        });
      }
    }

    // If payment is already verified, just return the order and payment details
    if (payment.status === "paid") {
      const order = await Order.findById(payment.orderId)
        .populate("userId", "name phone email")
        .populate("outletId", "name address")
        .populate("items.dishId", "name price");

      return res.status(200).json({
        success: true,
        message: "Payment already verified",
        data: {
          order,
          payment,
        },
      });
    }

    // If payment is not yet marked as paid
    return res.status(200).json({
      success: false,
      message: "Payment not yet completed",
      data: {
        payment,
      },
    });
  } catch (error) {
    console.error("Error verifying payment:", error);
    res.status(500).json({
      success: false,
      message: "Error verifying payment",
      error: error.message,
    });
  }
};

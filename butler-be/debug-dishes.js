import mongoose from "mongoose";
import Dish from "./models/Dish.js";
import dotenv from "dotenv";

dotenv.config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("MongoDB connected");
  } catch (error) {
    console.error("MongoDB connection error:", error);
    process.exit(1);
  }
};

const debugDishes = async () => {
  await connectDB();

  try {
    // Get total count of dishes
    const totalDishes = await Dish.countDocuments();
    console.log("Total dishes in database:", totalDishes);

    // Get available dishes
    const availableDishes = await Dish.countDocuments({ isAvailable: true });
    console.log("Available dishes:", availableDishes);

    // Get sample dishes
    const sampleDishes = await Dish.find().limit(5);
    console.log("Sample dishes:");
    sampleDishes.forEach((dish, index) => {
      console.log(
        `${index + 1}. ${dish.name} - Price: ${dish.price} - Available: ${
          dish.isAvailable
        } - Category: ${dish.category || "No category"} - Outlets: ${
          dish.outlets?.length || 0
        }`
      );
    });

    // Check for dishes with specific outlet
    const dishesWithOutlets = await Dish.find({
      outlets: { $exists: true, $ne: [] },
    }).limit(3);
    console.log("\nDishes with outlets:");
    dishesWithOutlets.forEach((dish, index) => {
      console.log(
        `${index + 1}. ${dish.name} - Outlets: ${dish.outlets.length}`
      );
    });

    // Check for dishes without outlets
    const dishesWithoutOutlets = await Dish.countDocuments({
      $or: [{ outlets: { $exists: false } }, { outlets: { $size: 0 } }],
    });
    console.log("\nDishes without outlets:", dishesWithoutOutlets);

    // Test the exact query used in the recommendation system
    console.log("\n=== Testing Recommendation Query ===");

    // Get a sample outlet ID from the dishes
    const sampleDish = await Dish.findOne({
      outlets: { $exists: true, $ne: [] },
    });
    if (sampleDish && sampleDish.outlets.length > 0) {
      const testOutletId = sampleDish.outlets[0];
      const testFoodChainId = sampleDish.foodChain;

      console.log("Testing with:");
      console.log("- Outlet ID:", testOutletId);
      console.log("- Food Chain ID:", testFoodChainId);

      // Test the exact query from getRecommendations
      const testQuery = {
        foodChain: testFoodChainId,
        isAvailable: true,
        outlets: testOutletId,
      };

      console.log("Query:", testQuery);

      const testResults = await Dish.find(testQuery);
      console.log("Results found:", testResults.length);

      if (testResults.length > 0) {
        console.log("Sample result:", {
          _id: testResults[0]._id,
          name: testResults[0].name,
          price: testResults[0].price,
          outlets: testResults[0].outlets,
        });
      }
    } else {
      console.log("No dishes with outlets found for testing");
    }
  } catch (error) {
    console.error("Error debugging dishes:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
};

debugDishes();

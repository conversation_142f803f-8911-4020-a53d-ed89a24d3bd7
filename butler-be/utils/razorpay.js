import Razorpay from "razorpay";
import dotenv from "dotenv";

dotenv.config();

// Initialize Razorpay with API keys from environment variables
const razorpay = new Razorpay({
  key_id: process.env.KEY_ID,
  key_secret: process.env.KEY_SECRET,
});

/**
 * Create a Razorpay payment link for an order
 * @param {Object} order - The order object
 * @param {String} orderId - The MongoDB order ID
 * @returns {Object} - The created payment link object
 */
export const createPaymentLink = async (order, orderId) => {
  try {
    // Use finalAmount if available (includes coupon discount), otherwise use totalAmount
    const paymentAmount =
      order.finalAmount !== undefined ? order.finalAmount : order.totalAmount;

    const options = {
      amount: Math.round(paymentAmount * 100), // Amount in smallest currency unit (paise)
      currency: "INR",
      accept_partial: false,
      description: `Payment for Order #${order.orderNumber}`,
      customer: {
        name: order.userId.name,
        contact: order.userId.phone,
      },
      notify: {
        sms: true,
        email: false,
      },
      reminder_enable: true,
      notes: {
        orderId: orderId,
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount.toString(),
        couponDiscount: (order.couponDiscount || 0).toString(),
        finalAmount: paymentAmount.toString(),
      },
      callback_url: `${process.env.CORS_ORIGIN}/payment-success`,
      callback_method: "get",
    };

    const paymentLink = await razorpay.paymentLink.create(options);
    return paymentLink;
  } catch (error) {
    console.error("Error creating Razorpay payment link:", error);
    throw error;
  }
};

/**
 * Update an existing Razorpay payment link with new amount
 * @param {String} paymentLinkId - The Razorpay payment link ID
 * @param {Object} order - The updated order object
 * @returns {Object} - The updated payment link object
 */
export const updatePaymentLink = async (paymentLinkId, order) => {
  try {
    // Use finalAmount if available (includes coupon discount), otherwise use totalAmount
    const paymentAmount =
      order.finalAmount !== undefined ? order.finalAmount : order.totalAmount;

    // Cancel the existing payment link first
    await razorpay.paymentLink.cancel(paymentLinkId);

    // Create a new payment link with updated amount
    const options = {
      amount: Math.round(paymentAmount * 100), // Amount in smallest currency unit (paise)
      currency: "INR",
      accept_partial: false,
      description: `Updated Payment for Order #${order.orderNumber}`,
      customer: {
        name: order.userId.name,
        contact: order.userId.phone,
      },
      notify: {
        sms: true,
        email: false,
      },
      reminder_enable: true,
      notes: {
        orderId: order._id.toString(),
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount.toString(),
        couponDiscount: (order.couponDiscount || 0).toString(),
        finalAmount: paymentAmount.toString(),
        updated: "true",
      },
      callback_url: `${process.env.CORS_ORIGIN}/payment-success`,
      callback_method: "get",
    };

    const newPaymentLink = await razorpay.paymentLink.create(options);
    return newPaymentLink;
  } catch (error) {
    console.error("Error updating Razorpay payment link:", error);
    throw error;
  }
};

/**
 * Fetch payment link details from Razorpay
 * @param {String} paymentLinkId - The Razorpay payment link ID
 * @returns {Object} - The payment link details
 */
export const getPaymentLinkDetails = async (paymentLinkId) => {
  try {
    const paymentLink = await razorpay.paymentLink.fetch(paymentLinkId);
    return paymentLink;
  } catch (error) {
    console.error("Error fetching Razorpay payment link:", error);
    throw error;
  }
};

/**
 * Verify Razorpay payment signature
 * @param {Object} options - The payment verification options
 * @returns {Boolean} - Whether the signature is valid
 */
export const verifyPaymentSignature = (options) => {
  try {
    const { razorpayOrderId, razorpayPaymentId, razorpaySignature } = options;
    const generatedSignature = crypto
      .createHmac("sha256", process.env.KEY_SECRET)
      .update(`${razorpayOrderId}|${razorpayPaymentId}`)
      .digest("hex");

    return generatedSignature === razorpaySignature;
  } catch (error) {
    console.error("Error verifying Razorpay payment signature:", error);
    return false;
  }
};

export default razorpay;

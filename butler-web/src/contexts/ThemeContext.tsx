// /contexts/ThemeContext.tsx
"use client";

import { ThemeConfig } from "@/app/type";
import { getThemeConfig } from "@/server/theme";
import { createContext, useContext, useEffect, useState } from "react";

const defaultTheme: ThemeConfig = {
  primaryColor: "#3B82F6", // default blue
  secondaryColor: "#1E40AF",
  accentColor: "#EF4444",
  logoUrl: "/default-logo.png",
  chainId: "",
  outletId: "",
  favIcon: "/favicon.ico",
  name: "<PERSON>",
};

type ThemeContextType = {
  theme: ThemeConfig;
  isLoading: boolean;
  setChainAndOutlet: (chainId: string, outletId?: string) => void;
};

const ThemeContext = createContext<ThemeContextType>({
  theme: defaultTheme,
  isLoading: true,
  setChainAndOutlet: () => {},
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);
  const [isLoading, setIsLoading] = useState(true);

  const fetchThemeConfig = async (chainId: string) => {
    if (!chainId) return;

    setIsLoading(true);
    try {
      const response = await getThemeConfig(chainId);
      if (!response.success) throw new Error("Failed to fetch theme");

      const themeData = response.data;
      setTheme({
        ...defaultTheme,
        ...themeData,
        chainId,
      });
    } catch (error) {
      console.error("Error fetching theme:", error);
      // Fall back to default theme with chain ID
      setTheme({
        ...defaultTheme,
        chainId,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const setChainAndOutlet = (chainId: string, outletId?: string) => {
    fetchThemeConfig(chainId);

    // Store outletId in localStorage if provided
    if (outletId) {
      localStorage.setItem("outletId", outletId);
    }
  };

  useEffect(() => {
    const storedChainId = localStorage.getItem("chainId");
    const params = new URLSearchParams(window.location.search);
    const urlChainId = params.get("chainId");
    const urlOutletId = params.get("outletId");

    // Store outletId from URL if available
    if (urlOutletId) {
      localStorage.setItem("outletId", urlOutletId);
    }

    if (urlChainId) {
      // URL parameters take precedence over stored values
      localStorage.setItem("chainId", urlChainId);
      fetchThemeConfig(urlChainId);
    } else if (storedChainId) {
      // Use stored chainId if no URL parameter
      fetchThemeConfig(storedChainId);
    } else {
      // No chain ID available, use default theme
      setIsLoading(false);
    }
  }, []);

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    // Remove the # if present
    hex = hex.replace(/^#/, "");

    // Parse the hex values
    let r, g, b;
    if (hex.length === 3) {
      // For shorthand like #ABC
      r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
      g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
      b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
    } else {
      // For full form like #AABBCC
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    }

    return { r, g, b };
  };

  // Apply theme to document
  useEffect(() => {
    if (!isLoading) {
      // Set primary color
      document.documentElement.style.setProperty(
        "--primary-color",
        theme.primaryColor
      );
      document.documentElement.style.setProperty(
        "--secondary-color",
        theme.secondaryColor
      );
      document.documentElement.style.setProperty(
        "--accent-color",
        theme.accentColor
      );

      // Convert primary color to RGB for the amoeba animation
      const primaryRgb = hexToRgb(theme.primaryColor);
      document.documentElement.style.setProperty(
        "--primary-color-rgb",
        `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.9)`
      );
      document.documentElement.style.setProperty(
        "--primary-color-rgb-70",
        `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.7)`
      );
      document.documentElement.style.setProperty(
        "--primary-color-rgb-40",
        `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.4)`
      );

      // Update favicon in the document head
      const link: HTMLLinkElement =
        document.querySelector("link[rel*='icon']") ||
        document.createElement("link");
      link.type = "image/x-icon";
      link.rel = "shortcut icon";
      // link.href = theme.favIcon || defaultTheme.favIcon;
      document.getElementsByTagName("head")[0].appendChild(link);
    }
  }, [theme, isLoading]);

  return (
    <ThemeContext.Provider value={{ theme, isLoading, setChainAndOutlet }}>
      <div
        style={{
          // Apply theme at the container level
          color: theme.primaryColor,
          // fontFamily: theme.fontFamily,
        }}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);

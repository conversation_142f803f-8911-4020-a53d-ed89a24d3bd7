"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  getAllRegistrationRequests,
  updateRegistrationRequestStatus,
  createFoodChainFromRequest,
} from "@/server/super-admin";
import {
  Loader2,
  Eye,
  CheckCircle,
  XCircle,
  Phone,
  Building2,
  Calendar,
  User,
  Mail,
  MapPin,
  Globe,
  MessageSquare,
} from "lucide-react";
import { format } from "date-fns";

interface RegistrationRequest {
  _id: string;
  contactPersonName: string;
  email: string;
  phone: string;
  businessName: string;
  businessType: string;
  subcategory: string;
  city: string;
  state: string;
  address?: string;
  message?: string;
  estimatedOutlets: number;
  website?: string;
  status: "pending" | "approved" | "rejected" | "contacted";
  adminNotes?: string;
  reviewedBy?: {
    name: string;
    email: string;
  };
  reviewedAt?: string;
  createdFoodChainId?: {
    name: string;
    status: string;
  };
  createdAt: string;
  updatedAt: string;
}

const RegistrationRequestsPage = () => {
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState<RegistrationRequest[]>([]);
  const [selectedRequest, setSelectedRequest] =
    useState<RegistrationRequest | null>(null);
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [actionLoading, setActionLoading] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [actionType, setActionType] = useState<
    "approved" | "rejected" | "contacted" | null
  >(null);

  const fetchRequests = async () => {
    setLoading(true);
    try {
      const response = await getAllRegistrationRequests({
        status: statusFilter === "all" ? undefined : statusFilter,
        page: currentPage,
        limit: 10,
      });

      if (response.success) {
        setRequests(response.data.requests);
        setTotalPages(response.data.pagination.totalPages);
      } else {
        toast.error("Failed to fetch registration requests");
      }
    } catch (error) {
      console.error("Error fetching requests:", error);
      toast.error("Error fetching registration requests");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [statusFilter, currentPage]);

  const handleStatusUpdate = async () => {
    if (!selectedRequest || !actionType) return;

    setActionLoading(true);
    try {
      const response = await updateRegistrationRequestStatus(
        selectedRequest._id,
        actionType,
        adminNotes
      );

      if (response.success) {
        toast.success(`Request ${actionType}d successfully`);
        setShowActionDialog(false);
        setAdminNotes("");
        setActionType(null);
        setSelectedRequest(null);
        fetchRequests();
      } else {
        toast.error(response.message || `Failed to ${actionType} request`);
      }
    } catch (error) {
      console.error("Error updating request:", error);
      toast.error(`Error ${actionType}ing request`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreateFoodChain = async () => {
    if (!selectedRequest) return;

    setActionLoading(true);
    try {
      const response = await createFoodChainFromRequest(selectedRequest._id, {
        tagline: `Welcome to ${selectedRequest.businessName}`,
      });

      if (response.success) {
        toast.success("Food chain created successfully");
        fetchRequests();
      } else {
        toast.error(response.message || "Failed to create food chain");
      }
    } catch (error) {
      console.error("Error creating food chain:", error);
      toast.error("Error creating food chain");
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", label: "Pending" },
      approved: { color: "bg-green-100 text-green-800", label: "Approved" },
      rejected: { color: "bg-red-100 text-red-800", label: "Rejected" },
      contacted: { color: "bg-blue-100 text-blue-800", label: "Contacted" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const openActionDialog = (
    request: RegistrationRequest,
    action: "approved" | "rejected" | "contacted"
  ) => {
    setSelectedRequest(request);
    setActionType(action);
    setAdminNotes("");
    setShowActionDialog(true);
  };

  const openDetailsDialog = (request: RegistrationRequest) => {
    setSelectedRequest(request);
    setShowDetailsDialog(true);
  };

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Registration Requests
          </h1>
          <p className="text-muted-foreground">
            Manage food chain registration requests
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="status-filter">Filter by Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="contacted">Contacted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Requests</CardTitle>
          <CardDescription>
            Review and manage food chain registration requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : requests.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No requests found
              </h3>
              <p className="text-gray-500">
                No registration requests match your current filters.
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Business Name</TableHead>
                    <TableHead>Contact Person</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {requests.map((request) => (
                    <TableRow key={request._id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {request.businessName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {request.businessType}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {request.contactPersonName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {request.email}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {request.city}, {request.state}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(request.status)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(request.createdAt), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDetailsDialog(request)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {request.status === "pending" && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  openActionDialog(request, "approved")
                                }
                                className="text-green-600 hover:text-green-700"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  openActionDialog(request, "rejected")
                                }
                                className="text-red-600 hover:text-red-700"
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                          {request.status === "approved" &&
                            !request.createdFoodChainId && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={handleCreateFoodChain}
                                disabled={actionLoading}
                              >
                                {actionLoading ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  "Create Chain"
                                )}
                              </Button>
                            )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                  <Button
                    variant="outline"
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(1, prev - 1))
                    }
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                    }
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Registration Request Details</DialogTitle>
            <DialogDescription>
              Complete information about the registration request
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-6">
              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Contact Person</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.contactPersonName}
                    </p>
                  </div>
                  <div>
                    <Label>Email</Label>
                    <p className="text-sm font-medium flex items-center gap-1">
                      <Mail className="h-4 w-4" />
                      {selectedRequest.email}
                    </p>
                  </div>
                  <div>
                    <Label>Phone</Label>
                    <p className="text-sm font-medium flex items-center gap-1">
                      <Phone className="h-4 w-4" />
                      {selectedRequest.phone}
                    </p>
                  </div>
                </div>
              </div>

              {/* Business Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Business Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Business Name</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.businessName}
                    </p>
                  </div>
                  {/* <div>
                    <Label>Business Type</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.businessType}
                    </p>
                  </div> */}
                  <div>
                    <Label>Category</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.subcategory}
                    </p>
                  </div>
                  <div>
                    <Label>Estimated Outlets</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.estimatedOutlets}
                    </p>
                  </div>
                  {selectedRequest.website && (
                    <div className="md:col-span-2">
                      <Label>Website</Label>
                      <p className="text-sm font-medium flex items-center gap-1">
                        <Globe className="h-4 w-4" />
                        <a
                          href={selectedRequest.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {selectedRequest.website}
                        </a>
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Location Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>City</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.city}
                    </p>
                  </div>
                  <div>
                    <Label>State</Label>
                    <p className="text-sm font-medium">
                      {selectedRequest.state}
                    </p>
                  </div>
                  {selectedRequest.address && (
                    <div className="md:col-span-2">
                      <Label>Full Address</Label>
                      <p className="text-sm font-medium">
                        {selectedRequest.address}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Additional Information */}
              {selectedRequest.message && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Additional Information
                  </h3>
                  <p className="text-sm bg-gray-50 p-3 rounded-md">
                    {selectedRequest.message}
                  </p>
                </div>
              )}

              {/* Status Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Status Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Current Status</Label>
                    <div className="mt-1">
                      {getStatusBadge(selectedRequest.status)}
                    </div>
                  </div>
                  <div>
                    <Label>Submitted Date</Label>
                    <p className="text-sm font-medium">
                      {format(
                        new Date(selectedRequest.createdAt),
                        "MMM dd, yyyy 'at' hh:mm a"
                      )}
                    </p>
                  </div>
                  {selectedRequest.reviewedBy && (
                    <>
                      <div>
                        <Label>Reviewed By</Label>
                        <p className="text-sm font-medium">
                          {selectedRequest.reviewedBy.name}
                        </p>
                      </div>
                      <div>
                        <Label>Reviewed Date</Label>
                        <p className="text-sm font-medium">
                          {selectedRequest.reviewedAt &&
                            format(
                              new Date(selectedRequest.reviewedAt),
                              "MMM dd, yyyy 'at' hh:mm a"
                            )}
                        </p>
                      </div>
                    </>
                  )}
                  {selectedRequest.adminNotes && (
                    <div className="md:col-span-2">
                      <Label>Admin Notes</Label>
                      <p className="text-sm bg-gray-50 p-3 rounded-md">
                        {selectedRequest.adminNotes}
                      </p>
                    </div>
                  )}
                  {selectedRequest.createdFoodChainId && (
                    <div className="md:col-span-2">
                      <Label>Created Food Chain</Label>
                      <p className="text-sm font-medium text-green-600">
                        {selectedRequest.createdFoodChainId.name} (
                        {selectedRequest.createdFoodChainId.status})
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Action Dialog */}
      <Dialog open={showActionDialog} onOpenChange={setShowActionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionType === "approved" && "Approve Request"}
              {actionType === "rejected" && "Reject Request"}
              {actionType === "contacted" && "Mark as Contacted"}
            </DialogTitle>
            <DialogDescription>
              {actionType === "approved" &&
                "Approve this registration request. You can create a food chain after approval."}
              {actionType === "rejected" &&
                "Reject this registration request. Please provide a reason."}
              {actionType === "contacted" &&
                "Mark this request as contacted. Add notes about the contact."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="admin-notes">
                {actionType === "rejected"
                  ? "Rejection Reason *"
                  : "Admin Notes (Optional)"}
              </Label>
              <Textarea
                id="admin-notes"
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={
                  actionType === "rejected"
                    ? "Please provide a reason for rejection..."
                    : "Add any notes about this action..."
                }
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowActionDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleStatusUpdate}
              disabled={
                actionLoading ||
                (actionType === "rejected" && !adminNotes.trim())
              }
              className={
                actionType === "approved"
                  ? "bg-green-600 hover:bg-green-700"
                  : actionType === "rejected"
                  ? "bg-red-600 hover:bg-red-700"
                  : "bg-blue-600 hover:bg-blue-700"
              }
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  {actionType === "approved" && (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  {actionType === "rejected" && (
                    <XCircle className="mr-2 h-4 w-4" />
                  )}
                  {actionType === "contacted" && (
                    <Phone className="mr-2 h-4 w-4" />
                  )}
                  {actionType === "approved" && "Approve"}
                  {actionType === "rejected" && "Reject"}
                  {actionType === "contacted" && "Mark as Contacted"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RegistrationRequestsPage;

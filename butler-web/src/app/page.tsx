"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  Check,
  Star,
  Users,
  Building2,
  ChefHat,
  Clock,
  Smartphone,
  TrendingUp,
} from "lucide-react";
import Image from "next/image";
import { useEffect } from "react";

export default function Home() {
  const router = useRouter();
  useEffect(() => {
    localStorage.removeItem("chainId");
    localStorage.removeItem("outletId");
  }, []);

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "<PERSON>",
    description: "AI-powered restaurant management and food ordering platform",
    url: "https://butler.com",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web, iOS, Android",
    offers: {
      "@type": "Offer",
      price: "1999",
      priceCurrency: "INR",
      priceValidUntil: "2025-12-31",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      ratingCount: "1250",
    },
    provider: {
      "@type": "Organization",
      name: "<PERSON>",
      url: "https://butler.com",
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
          <div className="flex items-center gap-2">
            <Image
              src="/logos/butler.png"
              alt="Butler Logo"
              width={32}
              height={32}
              className="w-8 h-8"
            />
            <div className="text-2xl font-bold text-blue-600">Butler</div>
          </div>
          <div className="space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push("/conversations")}
            >
              User Login
            </Button>
            <Button variant="default" onClick={() => router.push("/login")}>
              Chain Owner Login
            </Button>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-blue-50 to-white py-20">
          <div className="container mx-auto px-6 text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Your Complete Restaurant Management Solution
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Whether you&apos;re a restaurant chain owner or a food lover,
              Butler provides a seamless experience for managing and ordering
              from your favorite restaurants.
            </p>
            <div className="flex justify-center gap-4">
              <Button
                size="lg"
                onClick={() => router.push("/contact")}
                className="text-lg"
              >
                For Chain Owners
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => router.push("/conversations")}
                className="text-lg"
              >
                Order Food
              </Button>
            </div>
          </div>
        </section>

        {/* For Chain Owners Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-4">
              For Chain Owners
            </h2>
            <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
              Manage your entire restaurant chain with our comprehensive suite
              of tools
            </p>
            <div className="grid md:grid-cols-3 gap-8">
              {chainOwnerFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="p-6 border rounded-lg hover:shadow-lg transition-shadow bg-white"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* For Customers Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-4">
              For Food Lovers
            </h2>
            <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
              Experience a new way of discovering and ordering from your
              favorite restaurants
            </p>
            <div className="grid md:grid-cols-3 gap-8">
              {customerFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="p-6 border rounded-lg hover:shadow-lg transition-shadow bg-white"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-blue-600 text-white">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl font-bold mb-2">{stat.value}</div>
                  <div className="text-blue-100">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-4">
              Pricing Plans for Chain Owners
            </h2>
            <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
              Choose the perfect plan for your restaurant chain
            </p>
            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {pricingPlans.map((plan, index) => (
                <div
                  key={index}
                  className={`rounded-lg p-8 ${
                    plan.popular
                      ? "bg-blue-600 text-white shadow-lg scale-105"
                      : "bg-white border"
                  }`}
                >
                  {plan.popular && (
                    <div className="text-sm font-semibold text-white mb-2">
                      MOST POPULAR
                    </div>
                  )}
                  <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                  <div className="text-3xl font-bold mb-4">
                    ₹{plan.price}
                    <span className="text-sm font-normal">/month</span>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center gap-2"
                      >
                        <Check className="w-5 h-5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${
                      plan.popular ? "bg-white text-blue-600" : ""
                    }`}
                    variant={plan.popular ? "secondary" : "default"}
                    onClick={() => router.push("/super-admin-login")}
                  >
                    Get Started
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Success Stories */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-12">
              Success Stories
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-4">Chain Owners Love Us</h3>
                <div className="space-y-6">
                  {chainTestimonials.map((testimonial, index) => (
                    <div key={index} className="border-b pb-6 last:border-0">
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="w-5 h-5 text-yellow-400 fill-current"
                          />
                        ))}
                      </div>
                      <p className="text-gray-600 mb-4">{testimonial.quote}</p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Users className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-semibold">
                            {testimonial.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {testimonial.role}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-4">Customers Love Us</h3>
                <div className="space-y-6">
                  {customerTestimonials.map((testimonial, index) => (
                    <div key={index} className="border-b pb-6 last:border-0">
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="w-5 h-5 text-yellow-400 fill-current"
                          />
                        ))}
                      </div>
                      <p className="text-gray-600 mb-4">{testimonial.quote}</p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Users className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-semibold">
                            {testimonial.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {testimonial.role}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-600 text-white">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join the Butler platform and transform your restaurant business
              today
            </p>
            <div className="flex justify-center gap-4">
              <Button
                size="lg"
                variant="secondary"
                onClick={() => router.push("/contact#register")}
                className="text-blue-600"
              >
                Register Your Chain
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => router.push("/conversations")}
                className="text-blue-600"
              >
                Order Food
              </Button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-gray-300 py-12">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Image
                    src="/logos/butler.png"
                    alt="Butler Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                  <div className="text-xl font-bold text-white">Butler</div>
                </div>
                <p className="text-sm">
                  Transforming restaurant management with AI-powered solutions.
                </p>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Product</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/#features")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Features
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/#pricing")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Pricing
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/faq")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      FAQ
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/blog")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Blog
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/about")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      About Us
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/contact")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Contact
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/terms")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Terms of Service
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/privacy")}
                      className="hover:text-blue-200 transition-colors"
                    >
                      Privacy Policy
                    </button>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p>© {new Date().getFullYear()} Butler. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

const stats = [
  { value: "500+", label: "Restaurant Chains" },
  { value: "2000+", label: "Outlets" },
  { value: "1M+", label: "Orders Processed" },
  { value: "99.9%", label: "Uptime" },
];

const chainOwnerFeatures = [
  {
    title: "Centralized Management",
    description:
      "Manage all your outlets, menus, and staff from a single dashboard with real-time updates.",
    icon: Building2,
  },
  {
    title: "Smart Analytics",
    description:
      "Get detailed insights into sales, customer behavior, and operational efficiency across all outlets.",
    icon: TrendingUp,
  },
  {
    title: "AI-Powered Support",
    description:
      "Let our AI handle customer queries and orders, reducing staff workload and improving efficiency.",
    icon: ChefHat,
  },
];

const customerFeatures = [
  {
    title: "Easy Ordering",
    description:
      "Order from your favorite restaurants with just a few taps using our intuitive interface.",
    icon: Smartphone,
  },
  {
    title: "AI Chat Assistant",
    description:
      "Get personalized menu recommendations and instant responses to your queries.",
    icon: ChefHat,
  },
  {
    title: "Quick Service",
    description:
      "Experience faster order processing and real-time updates on your order status.",
    icon: Clock,
  },
];

const pricingPlans = [
  {
    name: "Starter",
    price: "1,999",
    features: [
      "Up to 2 Outlets",
      "Basic Analytics",
      "Menu Management",
      "24/7 Support",
      "Mobile App Access",
    ],
    popular: false,
  },
  {
    name: "Growth",
    price: "4,999",
    features: [
      "Up to 5 Outlets",
      "Advanced Analytics",
      "AI Chat Integration",
      "Priority Support",
      "Custom Branding",
      "API Access",
    ],
    popular: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    features: [
      "Unlimited Outlets",
      "Custom Analytics",
      "Dedicated Support",
      "White Label Solution",
      "Custom Integration",
      "SLA Guarantee",
    ],
    popular: false,
  },
];

const chainTestimonials = [
  {
    quote:
      "Butler has transformed how we manage our restaurant chain. The centralized dashboard and AI features have improved our efficiency by 40%.",
    name: "Rahul Sharma",
    role: "CEO, Spice Garden Chain",
  },
  {
    quote:
      "The analytics insights helped us optimize our menu and operations across all outlets. Our revenue has grown significantly.",
    name: "Priya Patel",
    role: "Operations Director, Food Express",
  },
];

const customerTestimonials = [
  {
    quote:
      "The AI chat feature makes ordering so easy. It remembers my preferences and makes great recommendations.",
    name: "Amit Kumar",
    role: "Regular Customer",
  },
  {
    quote:
      "Quick, reliable, and great customer service. The real-time order tracking is very helpful.",
    name: "Sarah Khan",
    role: "Food Enthusiast",
  },
];

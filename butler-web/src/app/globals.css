@import "tailwindcss";

@plugin "tailwindcss-animate";



@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.95 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

#amoeba {
  /* position: absolute; */
  width: 30px;
  height: 30px;
  background: radial-gradient(
      circle at 30% 30%,
      var(--primary-color-rgb, rgba(59, 130, 246, 0.9)),
      var(--primary-color-rgb-70, rgba(59, 130, 246, 0.7)) 40%,
      var(--primary-color-rgb-40, rgba(59, 130, 246, 0.4)) 70%
  );
  /* border: 3px solid var(--primary-color); */
  animation: shape-shift 2s infinite alternate ease-in-out;
}

@keyframes shape-shift {
  0% {
      border-radius: 50% 60% 70% 40% / 60% 50% 40% 70%;
      transform: scale(1) rotate(0deg);
  }
  50% {
      border-radius: 40% 70% 50% 60% / 50% 40% 70% 30%;
      transform: scale(1.1) rotate(10deg);
  }
  100% {
      border-radius: 60% 50% 40% 70% / 70% 60% 30% 50%;
      transform: scale(0.9) rotate(-10deg);
  }
}

#amoeba-landing-page {
  width: 30px;
  height: 30px;
  background: radial-gradient(
      circle at 30% 30%,
      rgba(39, 104, 207, 0.9),
      rgba(76, 89, 175, 0.7) 40%,
      rgba(84, 76, 175, 0.4) 70%
  );
  box-shadow:
      0 0 10px rgba(87, 76, 175, 0.5),
      0 0 20px rgba(94, 76, 175, 0.3);
  animation:
      shape-shift 1.5s infinite alternate ease-in-out,
      pulse 2s infinite alternate;
  position: relative;
}

#amoeba-landing-page::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: rgba(78, 76, 175, 0.2);
  border-radius: inherit;
  animation: ripple 2s infinite;
  z-index: -1;
}

@keyframes shape-shift {
  0% {
      border-radius: 50% 60% 70% 40% / 60% 50% 40% 70%;
      transform: scale(1) rotate(0deg);
  }
  50% {
      border-radius: 40% 70% 50% 60% / 50% 40% 70% 30%;
      transform: scale(1.1) rotate(10deg);
  }
  100% {
      border-radius: 60% 50% 40% 70% / 70% 60% 30% 50%;
      transform: scale(0.9) rotate(-10deg);
  }
}

@keyframes pulse {
  0% {
      transform: scale(1);
  }
  50% {
      transform: scale(1.05);
  }
  100% {
      transform: scale(0.95);
  }
}

@keyframes ripple {
  0% {
      transform: scale(0.8);
      opacity: 0.7;
  }
  100% {
      transform: scale(1.5);
      opacity: 0;
  }
}

/* Order Card Styles */
.order-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.2s;
}

.order-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border, #e0e0e0);
}

.order-id {
  font-weight: 600;
  font-size: 16px;
}

.order-date {
  color: var(--gray, #757575);
  font-size: 14px;
}

.status-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.order-content {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  color: var(--gray, #757575);
  margin-bottom: 8px;
}

.section-content {
  font-size: 15px;
}

.item-list {
  margin-top: 12px;
}

.item {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--light-gray, #f1f1f1);
  border-radius: 8px;
  margin-bottom: 8px;
}

.item-quantity {
  background-color: var(--primary-light, #e8f5e9);
  color: var(--primary, #4CAF50);
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.item-details {
  display: flex;
  align-items: center;
}

.item-price {
  font-weight: 500;
}

.total-amount {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  border-top: 1px solid var(--border, #e0e0e0);
  font-weight: 600;
}

.payment-info {
  padding: 16px 20px;
  border-top: 1px solid var(--border, #e0e0e0);
}

.payment-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.payment-label {
  color: var(--gray, #757575);
  font-size: 14px;
}

.payment-value {
  font-weight: 500;
}

.payment-method {
  color: var(--primary, #4CAF50);
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px 20px;
  border-top: 1px solid var(--border, #e0e0e0);
}
"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  Users,
  Target,
  Award,
  Heart,
  ChefHat,
  Building2,
  Smartphone,
  TrendingUp,
} from "lucide-react";
import Image from "next/image";
import Head from "next/head";

export default function AboutPage() {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>
          About Us - Butler | AI-Powered Restaurant Management Platform
        </title>
        <meta
          name="description"
          content="Learn about <PERSON>'s mission to revolutionize the restaurant industry with AI-powered solutions. Discover our story, values, and team behind the platform."
        />
        <meta
          name="keywords"
          content="about butler, restaurant technology, AI platform, company story, restaurant management team"
        />
        <meta
          property="og:title"
          content="About Butler - Restaurant Management Platform"
        />
        <meta
          property="og:description"
          content="Learn about <PERSON>'s mission to revolutionize the restaurant industry with AI-powered solutions."
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://butler.com/about" />
      </Head>
      <div className="min-h-screen">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 flex justify-between items-center px-6 py-4 bg-white/80 backdrop-blur-sm shadow-sm">
          <div className="flex items-center gap-2">
            <Image
              src="/logos/butler.png"
              alt="Butler Logo"
              width={32}
              height={32}
              className="w-8 h-8"
            />
            <div className="text-2xl font-bold text-blue-600">Butler</div>
          </div>
          <div className="space-x-4">
            <Button variant="ghost" onClick={() => router.push("/")}>
              Home
            </Button>
            <Button
              variant="ghost"
              onClick={() => router.push("/conversations")}
            >
              User Login
            </Button>
            <Button variant="default" onClick={() => router.push("/login")}>
              Chain Owner Login
            </Button>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-blue-50 to-white py-20">
          <div className="container mx-auto px-6 text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              About Butler
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              We&apos;re revolutionizing the restaurant industry with AI-powered
              solutions that make ordering seamless for customers and management
              effortless for restaurant owners.
            </p>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-2 gap-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Target className="w-8 h-8 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold mb-4">Our Mission</h2>
                <p className="text-gray-600 text-lg">
                  To transform the restaurant industry by providing intelligent,
                  user-friendly solutions that enhance customer experiences and
                  streamline restaurant operations through cutting-edge AI
                  technology.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Heart className="w-8 h-8 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold mb-4">Our Vision</h2>
                <p className="text-gray-600 text-lg">
                  To become the leading platform that connects restaurants and
                  customers worldwide, making food ordering as simple as having
                  a conversation while empowering restaurants with powerful
                  management tools.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-8">Our Story</h2>
              <div className="text-lg text-gray-600 space-y-6">
                <p>
                  Butler was born from a simple observation: ordering food
                  should be as easy as talking to a friend, and managing a
                  restaurant should be as intuitive as running a conversation.
                </p>
                <p>
                  Founded by a team of technology enthusiasts and restaurant
                  industry veterans, we recognized the gap between traditional
                  ordering systems and the modern customer&apos;s expectations.
                  We envisioned a world where AI could bridge this gap, making
                  interactions more natural and operations more efficient.
                </p>
                <p>
                  Today, Butler serves hundreds of restaurant chains and
                  thousands of customers, processing millions of orders with our
                  AI-powered platform. We&apos;re not just a technology company
                  – we&apos;re partners in the success of every restaurant and
                  the satisfaction of every customer.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Core Values */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-12">Our Values</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <div
                  key={index}
                  className="text-center p-6 border rounded-lg hover:shadow-lg transition-shadow"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <value.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-bold text-center mb-12">
              Meet Our Team
            </h2>
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {team.map((member, index) => (
                <div
                  key={index}
                  className="bg-white p-6 rounded-lg shadow-sm text-center"
                >
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-10 h-10 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{member.name}</h3>
                  <p className="text-blue-600 mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-600 text-white">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl font-bold mb-4">Join the Butler Family</h2>
            <p className="text-xl mb-8 opacity-90">
              Whether you&apos;re a restaurant owner or a food lover, we&apos;re
              here to make your experience better
            </p>
            <div className="flex justify-center gap-4">
              <Button
                size="lg"
                variant="secondary"
                onClick={() => router.push("/contact")}
                className="text-blue-600"
              >
                Partner With Us
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => router.push("/conversations")}
                className="text-blue-600"
              >
                Start Ordering
              </Button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-gray-300 py-12">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Image
                    src="/logos/butler.png"
                    alt="Butler Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                  <div className="text-xl font-bold text-white">Butler</div>
                </div>
                <p className="text-sm">
                  Transforming restaurant management with AI-powered solutions.
                </p>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Product</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/#features")}
                      className="hover:text-white transition-colors"
                    >
                      Features
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/#pricing")}
                      className="hover:text-white transition-colors"
                    >
                      Pricing
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/faq")}
                      className="hover:text-white transition-colors"
                    >
                      FAQ
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/about")}
                      className="hover:text-white transition-colors"
                    >
                      About Us
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/contact")}
                      className="hover:text-white transition-colors"
                    >
                      Contact
                    </button>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push("/terms")}
                      className="hover:text-white transition-colors"
                    >
                      Terms of Service
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push("/privacy")}
                      className="hover:text-white transition-colors"
                    >
                      Privacy Policy
                    </button>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p>© {new Date().getFullYear()} Butler. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

const values = [
  {
    title: "Innovation",
    description:
      "We continuously push the boundaries of technology to create solutions that didn't exist before.",
    icon: TrendingUp,
  },
  {
    title: "Customer First",
    description:
      "Every decision we make is guided by how it will improve the experience for our users.",
    icon: Heart,
  },
  {
    title: "Reliability",
    description:
      "We build robust, dependable systems that restaurants can count on every day.",
    icon: Award,
  },
];

const team = [
  {
    name: "Development Team",
    role: "Engineering",
    description:
      "Our talented engineers work tirelessly to build and maintain the Butler platform.",
  },
  {
    name: "Product Team",
    role: "Product Management",
    description:
      "Focused on understanding user needs and translating them into amazing features.",
  },
  {
    name: "Support Team",
    role: "Customer Success",
    description:
      "Dedicated to ensuring every restaurant and customer has the best possible experience.",
  },
];

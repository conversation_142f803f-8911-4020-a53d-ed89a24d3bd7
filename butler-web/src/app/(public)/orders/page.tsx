"use client";
import { useEffect, useState, useCallback } from "react";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ChevronLeft, LogOut, XCircle, Edit3, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { cancelOrder } from "@/server/user";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import OrderUpdateDialog from "@/components/orders/OrderUpdateDialog";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  isServed?: boolean;
  servedAt?: string;
  servedBy?: {
    _id: string;
    name: string;
  };
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  status: string;
  paymentStatus: string;
  createdAt: string;
  couponCode?: string;
  couponDiscount?: number;
  outletId: {
    name: string;
  };
}

const OrdersPage = () => {
  const router = useRouter();
  const { logout } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [cancelLoading, setCancelLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20, // Increased from 10 to 20 for better user experience
    total: 0,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const handleLogout = async () => {
    try {
      await logout();
      toast.success("Logged out successfully");
      // Redirect is handled in the logout function
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to log out. Please try again.");
    }
  };

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!selectedOrder) return;
    if (!cancellationReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }

    setCancelLoading(true);
    try {
      const response = await cancelOrder(selectedOrder._id, cancellationReason);
      if (response.success) {
        toast.success("Order cancelled successfully");
        setShowCancelDialog(false);

        // Update the order in the list
        setOrders(
          orders.map((order) =>
            order._id === selectedOrder._id
              ? { ...order, status: "cancelled" }
              : order
          )
        );
      } else {
        toast.error(response.message || "Failed to cancel order");
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast.error("An error occurred while cancelling the order");
    } finally {
      setCancelLoading(false);
    }
  };

  // Open cancel dialog for an order
  const openCancelDialog = (order: Order) => {
    setSelectedOrder(order);
    setShowCancelDialog(true);
    setCancellationReason("");
  };

  const fetchOrders = useCallback(
    async (page = 1, append = false) => {
      try {
        if (append) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders?page=${page}&limit=${pagination.limit}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
          }
        );
        const data = await response.json();

        if (data.success) {
          if (append) {
            setOrders((prev) => [...prev, ...data.data]);
          } else {
            setOrders(data.data);
          }
          setPagination(data.pagination);
        }
      } catch (error) {
        console.error("Error fetching orders:", error);
        toast.error("Failed to load orders");
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [pagination.limit]
  );

  const loadMoreOrders = () => {
    if (pagination.hasNext && !loadingMore) {
      fetchOrders(pagination.page + 1, true);
    }
  };

  const openUpdateDialog = (order: Order) => {
    setSelectedOrder(order);
    setShowUpdateDialog(true);
  };

  const handleOrderUpdated = () => {
    // Refresh the orders list
    fetchOrders(1, false);
  };

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const getStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-purple-100 text-purple-800",
      ready: "bg-green-100 text-green-800",
      delivered: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading...
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <Button
          variant="outline"
          onClick={() =>
            localStorage.getItem("to-chat")
              ? router.back()
              : router.push("/conversations")
          }
        >
          <ChevronLeft className="mr-1 h-4 w-4" /> Back
        </Button>
        <Button variant="outline" onClick={handleLogout}>
          <LogOut className="mr-1 h-4 w-4" /> Logout
        </Button>
      </div>
      <h1 className="text-2xl font-bold mb-6">Order History</h1>
      <div className="space-y-4">
        {orders.map((order) => (
          <Card key={order._id} className="border-2 border-gray-400">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div
                  className="hover:underline"
                  onClick={() => router.push(`/order-tracking/${order._id}`)}
                >
                  <CardTitle>Order #{order.orderNumber}</CardTitle>
                  <CardDescription>
                    {format(new Date(order.createdAt), "PPP")}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {order.paymentStatus !== "paid" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openUpdateDialog(order)}
                    >
                      <Edit3 className="mr-1 h-4 w-4" />
                      Update
                    </Button>
                  )}
                  <Badge className={getStatusColor(order.status)}>
                    {order.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Outlet</p>
                  <p className="text-sm text-gray-500">{order.outletId.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-2">Items</p>
                  <div className="space-y-2">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>
                          {item.dishId?.name || item.dishName || "Deleted Dish"}{" "}
                          x {item.quantity}
                        </span>
                        <span>₹{item.price}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between pt-4 border-t border-gray-400">
                  <div className="flex flex-col gap-2">
                    <span className="text-sm text-gray-500">
                      {order.createdAt
                        ? new Date(order.createdAt).toLocaleDateString()
                        : ""}
                    </span>
                    <span className="font-medium">
                      {order.couponCode && (order.couponDiscount || 0) > 0
                        ? "Final Amount"
                        : "Total Amount"}
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 items-end">
                    {order.couponCode && (order.couponDiscount || 0) > 0 && (
                      <div className="text-right">
                        <div className="text-sm text-gray-500">
                          Subtotal: ₹{order.totalAmount}
                        </div>
                        <div className="text-sm text-green-600">
                          Discount ({order.couponCode}): -₹
                          {order.couponDiscount || 0}
                        </div>
                      </div>
                    )}
                    <span className="font-bold text-lg">
                      ₹
                      {order.finalAmount ||
                        order.totalAmount - (order.couponDiscount || 0)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between gap-2">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/order-tracking/${order._id}`)}
                >
                  Track Order
                </Button>
              </div>
              {order.status === "pending" && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => openCancelDialog(order)}
                >
                  <XCircle className="mr-1 h-4 w-4" />
                  Cancel
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Load More Button */}
      {pagination.hasNext && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={loadMoreOrders}
            disabled={loadingMore}
            className="min-w-32"
          >
            {loadingMore ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Orders"
            )}
          </Button>
        </div>
      )}

      {/* Order Update Dialog */}
      {selectedOrder && (
        <OrderUpdateDialog
          order={selectedOrder}
          isOpen={showUpdateDialog}
          onClose={() => setShowUpdateDialog(false)}
          onSuccess={handleOrderUpdated}
        />
      )}

      {/* Cancel Order Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
            <DialogDescription>
              Please provide a reason for cancelling this order.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for cancellation</Label>
              <Textarea
                id="reason"
                placeholder="Please explain why you're cancelling this order"
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
              disabled={cancelLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCancelOrder}
              disabled={cancelLoading}
              variant="destructive"
            >
              {cancelLoading ? "Cancelling..." : "Confirm Cancellation"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrdersPage;

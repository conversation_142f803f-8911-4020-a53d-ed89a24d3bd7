"use client";
import { Dish } from "@/app/type";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

interface CartData {
  items: Dish[];
  foodChainId: string;
  outletId: string;
  updatedAt: number;
}

const useCart = () => {
  const [cart, setCart] = useState<Dish[]>([]);
  const params = useSearchParams();

  // Get foodChainId and outletId from URL params or localStorage
  const getFoodChainId = (): string => {
    const urlChainId = params?.get("chainId");
    if (urlChainId) return urlChainId;

    return localStorage.getItem("chainId") || "";
  };

  const getOutletId = (): string => {
    const urlOutletId = params?.get("outletId");
    if (urlOutletId) return urlOutletId;

    return localStorage.getItem("outletId") || "";
  };

  // Generate a unique key for the current food chain and outlet
  const getCartKey = (): string => {
    const foodChainId = getFoodChainId();
    const outletId = getOutletId();

    if (!foodChainId) return "cart"; // Fallback to original key for backward compatibility

    return `cart_${foodChainId}${outletId ? `_${outletId}` : ""}`;
  };

  // Load cart data on component mount
  useEffect(() => {
    // First, try to get the cart key based on URL parameters
    const cartKey = getCartKey();

    // Try to get cart data with the new format
    const storedCart = localStorage.getItem(cartKey);

    if (storedCart) {
      try {
        const parsedData = JSON.parse(storedCart);

        if (parsedData.items) {
          setCart(parsedData.items);
        } else {
          setCart(parsedData);
        }
      } catch (error) {
        console.error("Error parsing cart data:", error);
        setCart([]);
      }
    } else {
      // If no cart found with the current key, try to find any cart for the current chain
      localStorage.setItem(
        cartKey,
        JSON.stringify({
          items: [],
          foodChainId: getFoodChainId(),
          outletId: getOutletId(),
          updatedAt: Date.now(),
        })
      );
      // This helps when URL parameters are lost during navigation
      // const foodChainId = getFoodChainId();

      // if (foodChainId) {
      //   // Look for any cart with this chain ID
      //   const cartKeyPattern = `cart_${foodChainId}`;

      //   // Find all localStorage keys that match our pattern
      //   const matchingKeys = Object.keys(localStorage).filter((key) =>
      //     key.startsWith(cartKeyPattern)
      //   );

      //   if (matchingKeys.length > 0) {
      //     // Use the first matching cart we find
      //     try {
      //       const matchingCart = localStorage.getItem(matchingKeys[0]);
      //       if (matchingCart) {
      //         const parsedData = JSON.parse(matchingCart);

      //         if (parsedData.items) {
      //           setCart(parsedData.items);

      //           // Update the cart with the current outlet ID if needed
      //           const currentOutletId = getOutletId();
      //           if (
      //             currentOutletId &&
      //             parsedData.outletId !== currentOutletId
      //           ) {
      //             const updatedCartData: CartData = {
      //               ...parsedData,
      //               outletId: currentOutletId,
      //               updatedAt: Date.now(),
      //             };
      //             localStorage.setItem(
      //               cartKey,
      //               JSON.stringify(updatedCartData)
      //             );
      //           }
      //         } else {
      //           setCart(parsedData);
      //         }
      //       }
      //     } catch (error) {
      //       console.error("Error parsing matching cart data:", error);
      //     }
      //   } else {
      //     // Check for legacy cart data as a last resort
      //     const legacyCart = localStorage.getItem("cart");
      //     if (legacyCart) {
      //       try {
      //         const parsedLegacyCart = JSON.parse(legacyCart);
      //         setCart(parsedLegacyCart);

      //         // Migrate legacy cart to new format
      //         const cartData: CartData = {
      //           items: parsedLegacyCart,
      //           foodChainId,
      //           outletId: getOutletId(),
      //           updatedAt: Date.now(),
      //         };
      //         localStorage.setItem(cartKey, JSON.stringify(cartData));
      //         localStorage.removeItem("cart"); // Remove legacy cart
      //       } catch (error) {
      //         console.error("Error parsing legacy cart data:", error);
      //         setCart([]);
      //       }
      //     } else {
      //       setCart([]);
      //     }
      //   }
      // } else {
      //   // No chain ID available, check for legacy cart
      //   const legacyCart = localStorage.getItem("cart");
      //   if (legacyCart) {
      //     try {
      //       const parsedLegacyCart = JSON.parse(legacyCart);
      //       setCart(parsedLegacyCart);
      //     } catch (error) {
      //       console.error("Error parsing legacy cart data:", error);
      //       setCart([]);
      //     }
      //   } else {
      //     setCart([]);
      //   }
      // }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  // Save cart data when it changes
  useEffect(() => {
    if (cart.length > 0) {
      const cartKey = getCartKey();
      const foodChainId = getFoodChainId();
      const outletId = getOutletId();

      // Only save with metadata if we have a food chain ID
      if (foodChainId) {
        const cartData: CartData = {
          items: cart,
          foodChainId,
          outletId,
          updatedAt: Date.now(),
        };
        localStorage.setItem(cartKey, JSON.stringify(cartData));
      } else {
        // Fallback to original format for backward compatibility
        localStorage.setItem("cart", JSON.stringify(cart));
      }
    }
    // else {
    //   // If cart is empty, remove the item from localStorage
    //   const cartKey = getCartKey();
    //   localStorage.removeItem(cartKey);
    //   // Also remove legacy cart if it exists
    //   localStorage.removeItem("cart");
    // }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cart]);

  const clearCart = () => {
    setCart([]);
    const cartKey = getCartKey();
    localStorage.removeItem(cartKey);
    localStorage.removeItem("cart"); // Also remove legacy cart
  };

  return {
    cart,
    setCart,
    clearCart,
  };
};

export default useCart;

/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dish } from "@/app/type";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerContent,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { DropdownMenuLabel } from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { getOutletMenu } from "@/server/user";
import { MessageCircle } from "lucide-react";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

const groupDishesByCategory = (dishes: Dish[]) => {
  // Create an object to hold grouped dishes
  const groupedDishes: any = {};

  // Group dishes by their category
  dishes.forEach((dish: any) => {
    const category = dish?.category?.name || "-";
    if (!groupedDishes[category]) {
      groupedDishes[category] = [];
    }
    groupedDishes[category].push(dish);
  });

  // Convert to array format if needed
  return Object.entries(groupedDishes).map(([category, dishes]) => ({
    category,
    dishes,
  }));
};

const ShowMenuDrawer = ({
  outletId,
  foodChainId,
  text,
}: {
  outletId: string;
  foodChainId: string;
  text: string;
}) => {
  const router = useRouter();
  const [menu, setMenu] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const fetchMenu = async () => {
    const menu = await getOutletMenu(foodChainId, outletId);
    const groupedMenu = groupDishesByCategory(menu.data);
    setMenu(groupedMenu);
  };

  useEffect(() => {
    fetchMenu();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  return (
    <Drawer direction="right">
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm">
          {text}
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle className="flex justify-between items-center">
            Menu
            <Button
              //   variant="ghost"
              size="sm"
              className="cursor-pointer"
              onClick={() => {
                localStorage.setItem("to-outlets", "true");
                router.push(
                  `/chat?chainId=${foodChainId}&outletId=${outletId}`
                );
              }}
            >
              <MessageCircle className="h-4 w-4" />
            </Button>
          </DrawerTitle>
        </DrawerHeader>
        <ScrollArea className="max-h-16 border-b ">
          {menu.map((category, index) => (
            <button
              key={index}
              onClick={() => {
                handleCategorySelect(
                  category.category == selectedCategory ? "" : category.category
                );
                document
                  .getElementById(category.category)
                  ?.scrollIntoView({ behavior: "smooth" });
              }}
              className={`px-3 py-2 mb-1 mr-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                selectedCategory === category.category
                  ? "bg-gray-200 font-medium"
                  : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {category.category}
            </button>
          ))}
        </ScrollArea>
        <ScrollArea className="max-h-[70vh] overflow-y-scroll">
          {menu.map((category, categoryIndex) => (
            <div
              key={categoryIndex}
              id={`category-${category.category}`}
              className={`py-2 ${
                selectedCategory && selectedCategory !== category.category
                  ? "opacity-60"
                  : ""
              }`}
            >
              <DropdownMenuLabel
                className="text-base font-bold px-4 py-2 flex justify-between items-center"
                id={category.category}
              >
                {category.category}
                <span className="text-xs text-gray-500">
                  {category.dishes.length} items
                </span>
              </DropdownMenuLabel>

              {category.dishes.map((dish: Dish, dishIndex: number) => (
                <div key={dishIndex} className="relative">
                  <div className="flex justify-between px-4 py-3 hover:bg-gray-50">
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <div className="font-medium">{dish.name}</div>
                        <Badge variant="outline" className="font-bold mr-2">
                          ₹{dish.price}
                        </Badge>
                      </div>
                      {dish.description && (
                        <div className="text-xs text-gray-500 mt-1 pr-8">
                          {dish.description}
                        </div>
                      )}
                    </div>
                  </div>
                  {dishIndex < category.dishes.length - 1 && (
                    <Separator className="my-0 mx-4" />
                  )}
                </div>
              ))}

              {categoryIndex < menu.length - 1 && (
                <Separator className="my-2" />
              )}
            </div>
          ))}
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
};

export default ShowMenuDrawer;

"use client";
import { Volume2, Ban } from "lucide-react";
import { useState, useEffect } from "react";

interface TextToSpeechOptions {
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: string; // voice name
}

interface TextToSpeechProps {
  text: string;
  options?: TextToSpeechOptions;
  disabled?: boolean;
  className?: string;
  onStart?: () => void;
  onEnd?: () => void;
  onError?: () => void;
}

export default function TextToSpeech({
  text,
  options = {},
  disabled = false,
  className = "",
  onStart,
  onEnd,
  onError,
}: TextToSpeechProps) {
  const [isReading, setIsReading] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);

  const { rate = 1, pitch = 1, volume = 1, voice: voiceName } = options;

  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      setVoices(availableVoices);
    };

    loadVoices();
    window.speechSynthesis.addEventListener("voiceschanged", loadVoices);

    return () => {
      window.speechSynthesis.removeEventListener("voiceschanged", loadVoices);
    };
  }, []);

  const handleReadText = () => {
    if (!("speechSynthesis" in window)) {
      alert("Your browser does not support text-to-speech");
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);

    // Set voice if specified
    if (voiceName) {
      const selectedVoice = voices.find((v) => v.name === voiceName);
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }
    }

    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;

    utterance.onstart = () => {
      setIsReading(true);
      onStart?.();
    };

    utterance.onend = () => {
      setIsReading(false);
      onEnd?.();
    };

    utterance.onerror = () => {
      setIsReading(false);
      onError?.();
    };

    window.speechSynthesis.speak(utterance);
  };

  const handleStopReading = () => {
    window.speechSynthesis.cancel();
    setIsReading(false);
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      <button
        onClick={() => handleReadText()}
        disabled={disabled || isReading || !text.trim()}
        className="px-4 py-2 text-white rounded disabled:bg-gray-300"
      >
        {isReading ? (
          <div className="animate-pulse">
            <Volume2 />
          </div>
        ) : (
          <Volume2 className="cursor-pointer" />
        )}
      </button>

      {isReading && (
        <button
          onClick={handleStopReading}
          className="px-4 py-2 bg-red-500 text-white rounded disabled:bg-gray-300"
        >
          <Ban />
        </button>
      )}
    </div>
  );
}

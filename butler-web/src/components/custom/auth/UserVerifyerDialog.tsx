"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Icon } from "@iconify/react";
import { useAuth } from "@/contexts/AuthContext";
import FirstLoginDialog from "@/components/custom/auth/FirstLoginDialog";
import GoogleSignIn from "@/components/auth/GoogleSignIn";
import { userRegister } from "@/server/user";

interface FormData {
  name: string;
  email: string;
  phone: string;
  password: string;
}

const UserVerifierDialog = () => {
  const [open, setOpen] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showFirstLoginDialog, setShowFirstLoginDialog] = useState(false);
  const { login } = useAuth();

  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    password: "",
  });

  useEffect(() => {
    const userToken = localStorage.getItem("user-token");
    if (userToken) return;

    const publicRoutes = new Set([
      "/",
      "/admin",
      "/super-admin",
      "/login",
      "/contact",
      "/faq",
      "/privacy",
      "/terms",
    ]);

    const currentPath = window.location.pathname;
    const isPublicRoute =
      publicRoutes.has(currentPath) ||
      currentPath.startsWith("/admin") ||
      currentPath.startsWith("/super-admin");

    if (!isPublicRoute) {
      setOpen(true);
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const validateForm = () => {
    if (isLogin) {
      if (!formData.email.trim() || !formData.password.trim()) {
        toast.error("Please fill in all required fields");
        return false;
      }
    } else {
      if (
        !formData.name.trim() ||
        !formData.email.trim() ||
        !formData.phone.trim() ||
        !formData.password.trim()
      ) {
        toast.error("Please fill in all required fields");
        return false;
      }
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return false;
    }

    // Password validation
    if (formData.password.length < 6) {
      toast.error("Password must be at least 6 characters long");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      if (isLogin) {
        // Handle login using AuthContext
        const loginResult = await login(
          formData.email,
          formData.password,
          "user"
        );

        if (loginResult.success) {
          // Check if this is a first-time login
          if (loginResult.isFirstTimeLogin) {
            // Show the first login dialog
            setShowFirstLoginDialog(true);
          } else {
            // Regular login
            toast.success("Logged in successfully");
            setOpen(false);
            window.location.reload();
          }
        } else {
          toast.error("Invalid credentials");
        }
      } else {
        // For registration, we'll continue to use the direct API call
        // since the AuthContext doesn't have a register function
        try {
          // First register the user
          const registerResult = await userRegister(
            formData.email,
            formData.password,
            formData.name,
            formData.phone
          );

          if (registerResult.success) {
            // After successful registration, log the user in
            const loginResult = await login(
              formData.email,
              formData.password,
              "user"
            );

            if (loginResult.success) {
              // Make sure userId is stored in localStorage
              if (loginResult.user?.id) {
                localStorage.setItem("userId", loginResult.user.id);
              }

              toast.success("Registration successful");
              setOpen(false);
              window.location.reload();
            } else {
              toast.error(
                "Registration successful but login failed. Please try logging in."
              );
              setOpen(false);
            }
          } else {
            // Registration failed
            toast.error(registerResult.message || "Registration failed");
          }
        } catch (regError) {
          console.error("Registration error:", regError);
          toast.error("Registration failed");
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Handle close of first login dialog
  const handleFirstLoginClose = () => {
    setShowFirstLoginDialog(false);
    window.location.reload();
  };

  // Handle Google Sign-In success
  const handleGoogleSuccess = () => {
    toast.success("Successfully signed in with Google!");
    setOpen(false);
    window.location.reload();
  };

  // Handle Google Sign-In error
  const handleGoogleError = (error: string) => {
    toast.error(error);
  };

  return (
    <>
      {/* First Login Dialog */}
      {showFirstLoginDialog && (
        <FirstLoginDialog
          open={showFirstLoginDialog}
          onClose={handleFirstLoginClose}
          userType="user"
          email={formData.email}
        />
      )}

      <Dialog open={open}>
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{isLogin ? "Login" : "Register"}</DialogTitle>
            <DialogDescription>
              {isLogin
                ? "Sign in to your account to continue"
                : "Create a new account to get started"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-2">
            {!isLogin && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your name"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                  />
                </div>
              </>
            )}

            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder={
                    isLogin ? "Enter your password" : "Create a password"
                  }
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  <Icon
                    icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                    className="w-5 h-5 text-gray-500"
                  />
                </button>
              </div>
            </div>

            {/* Google Sign-In */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>

            <GoogleSignIn
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              disabled={loading}
            />
          </div>

          <DialogFooter>
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Icon
                    icon="lucide:loader-2"
                    className="w-4 h-4 animate-spin"
                  />
                  <span>{isLogin ? "Logging in..." : "Registering..."}</span>
                </div>
              ) : (
                <span>{isLogin ? "Login" : "Register"}</span>
              )}
            </Button>
            <br />
            <p className="text-sm text-center">
              {isLogin ? "Don't have an account?" : "Already have an account?"}{" "}
              <button
                onClick={() => setIsLogin(!isLogin)}
                className="text-primary hover:underline"
              >
                {isLogin ? "Register" : "Login"}
              </button>
            </p>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UserVerifierDialog;

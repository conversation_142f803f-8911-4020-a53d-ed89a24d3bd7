"use client";

import React, { useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface GoogleSignInProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

declare global {
  interface Window {
    google: any;
  }
}

const GoogleSignIn: React.FC<GoogleSignInProps> = ({
  onSuccess,
  onError,
  disabled = false,
  className = "",
}) => {
  const { loginWithGoogle } = useAuth();
  const [loading, setLoading] = React.useState(false);
  const [googleLoaded, setGoogleLoaded] = React.useState(false);
  const scriptLoadedRef = useRef(false);
  const buttonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadGoogleScript();
  }, []);

  const loadGoogleScript = async () => {
    if (scriptLoadedRef.current) return;

    try {
      const script = document.createElement("script");
      script.src = "https://accounts.google.com/gsi/client";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        scriptLoadedRef.current = true;
        initializeGoogle();
      };

      script.onerror = () => {
        console.error("Failed to load Google Sign-In script");
        toast.error("Failed to load Google Sign-In");
      };

      document.head.appendChild(script);
    } catch (error) {
      console.error("Error loading Google script:", error);
      toast.error("Failed to initialize Google Sign-In");
    }
  };

  const initializeGoogle = () => {
    try {
      if (!process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID) {
        console.error("Google Client ID not found");
        toast.error("Google Sign-In configuration error");
        return;
      }

      // Initialize without using prompt() to avoid FedCM
      window.google.accounts.id.initialize({
        client_id: process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID,
        callback: handleGoogleResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
        // Explicitly disable FedCM
        use_fedcm_for_prompt: false,
        itp_support: false,
      });

      setGoogleLoaded(true);
    } catch (error) {
      console.error("Failed to initialize Google Sign-In:", error);
      toast.error("Failed to initialize Google Sign-In");
    }
  };

  const handleGoogleResponse = async (response: any) => {
    if (!response.credential) {
      console.error("No credential received from Google");
      toast.error("Authentication failed");
      return;
    }

    setLoading(true);
    try {
      const userInfo = parseJwt(response.credential);

      if (!userInfo) {
        throw new Error("Failed to parse Google token");
      }

      const userData = {
        googleId: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
      };

      const result = await loginWithGoogle(response.credential, userData);

      if (result.success) {
        toast.success("Successfully signed in with Google!");
        onSuccess?.();
      } else {
        throw new Error(result.error || "Google sign-in failed");
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Google sign-in failed";
      toast.error(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const parseJwt = (token: string) => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error parsing JWT:", error);
      return null;
    }
  };

  const handleGoogleSignIn = async () => {
    if (!googleLoaded) {
      toast.error("Google Sign-In is still loading...");
      return;
    }

    if (!window.google || !window.google.accounts) {
      toast.error("Google Sign-In is not available");
      return;
    }

    try {
      // Create a hidden div to render the Google button
      const hiddenDiv = document.createElement("div");
      hiddenDiv.style.position = "absolute";
      hiddenDiv.style.top = "-1000px";
      hiddenDiv.style.left = "-1000px";
      hiddenDiv.style.width = "1px";
      hiddenDiv.style.height = "1px";
      hiddenDiv.style.overflow = "hidden";
      document.body.appendChild(hiddenDiv);

      // Render the Google button in the hidden div
      window.google.accounts.id.renderButton(hiddenDiv, {
        theme: "outline",
        size: "large",
        type: "standard",
        shape: "rectangular",
        text: "signin_with",
        logo_alignment: "left",
        width: 250,
      });

      // Wait a moment for the button to render
      setTimeout(() => {
        const googleButton = hiddenDiv.querySelector(
          'div[role="button"]'
        ) as HTMLElement;
        if (googleButton) {
          // Trigger the click programmatically
          googleButton.click();
        } else {
          console.error("Google button not found");
          toast.error("Unable to trigger Google Sign-In");
        }

        // Clean up the hidden div
        setTimeout(() => {
          if (document.body.contains(hiddenDiv)) {
            document.body.removeChild(hiddenDiv);
          }
        }, 1000);
      }, 100);
    } catch (error) {
      console.error("Error triggering Google Sign-In:", error);
      toast.error("Unable to show Google Sign-In");
    }
  };

  return (
    <Button
      type="button"
      variant="outline"
      onClick={handleGoogleSignIn}
      disabled={disabled || loading || !googleLoaded}
      className={`w-full flex items-center justify-center gap-2 ${className}`}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <svg className="h-4 w-4" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      )}
      {loading
        ? "Signing in..."
        : googleLoaded
        ? "Continue with Google"
        : "Loading..."}
    </Button>
  );
};

export default GoogleSignIn;

"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Loader2, Phone, MapPin } from "lucide-react";
import { completeUserProfile } from "@/server/user";

interface ProfileCompletionDialogProps {
  open: boolean;
  onComplete: (userData: any) => void;
  userEmail?: string;
  userName?: string;
}

export default function ProfileCompletionDialog({
  open,
  onComplete,
  userEmail,
  userName,
}: ProfileCompletionDialogProps) {
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [loading, setLoading] = useState(false);

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "");
    
    // Add +91 prefix if not present and format
    if (digits.length <= 10) {
      return digits.length > 0 ? `+91 ${digits}` : "";
    }
    
    // If already has country code, format accordingly
    if (digits.startsWith("91") && digits.length <= 12) {
      return `+${digits.slice(0, 2)} ${digits.slice(2)}`;
    }
    
    return value;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phone.trim()) {
      toast.error("Phone number is required");
      return;
    }

    // Basic phone validation
    const phoneDigits = phone.replace(/\D/g, "");
    if (phoneDigits.length < 10) {
      toast.error("Please enter a valid phone number");
      return;
    }

    setLoading(true);
    try {
      const response = await completeUserProfile(phone.trim(), address.trim());

      if (response.success) {
        toast.success("Profile completed successfully!");
        
        // Update localStorage with new token
        localStorage.setItem("user-token", response.data.token);
        
        // Update cookie
        document.cookie = `user-token=${response.data.token}; path=/; max-age=86400; secure; samesite=strict`;
        
        onComplete(response.data.user);
      } else {
        toast.error(response.message || "Failed to complete profile");
      }
    } catch (error) {
      console.error("Profile completion error:", error);
      toast.error("An error occurred while completing your profile");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="text-center">Complete Your Profile</DialogTitle>
          <DialogDescription className="text-center">
            Welcome {userName}! Please provide your phone number to complete your account setup.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={userEmail || ""}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number *</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="phone"
                type="tel"
                placeholder="+91 **********"
                value={phone}
                onChange={(e) => setPhone(formatPhoneNumber(e.target.value))}
                className="pl-10"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address (Optional)</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Textarea
                id="address"
                placeholder="Enter your address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="pl-10 min-h-[80px]"
                rows={3}
              />
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Completing Profile...
              </>
            ) : (
              "Complete Profile"
            )}
          </Button>
        </form>

        <div className="text-xs text-gray-500 text-center mt-4">
          Your phone number will be used for order notifications and account security.
        </div>
      </DialogContent>
    </Dialog>
  );
}
